const { generateSingleQuestion } = require("../utils/helpers");

exports.generateNextQuestion = async (req, res) => {
  const MIN_QUESTIONS = 5;
  
  try {
    const { role, skills, previousQuestions, companyType, experience, videoProfileId } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Parse and validate previous questions array
    // Frontend sends previousQuestions as an array of objects, each containing question and answer
    const previousQA = (previousQuestions || []).map((q) => ({
      question: q.question,
      answer: q.answer || null, // Directly use the answer from the question object
      type: q.type,
      question_number: q.question_number,
      total_questions: q.total_questions
    }));

    console.log("Previous Questions:", previousQA);

    let totalQuestions = 10; // Default maximum

    const questionCount = (previousQuestions || []).length + 1;

    console.log(`📊 INTERVIEW PROGRESS: Question ${questionCount} of ${totalQuestions}`);

    // Simple count-based interview termination - interview ends after exactly N questions
    if (questionCount > totalQuestions) {
      console.log(`🏁 INTERVIEW COMPLETED: Attempting to generate question ${questionCount} but max is ${totalQuestions} (${questionCount - 1} completed)`);
      return res.status(200).json({
        success: false,
        message: "Interview completed",
        completed: true,
        totalQuestionsCompleted: questionCount - 1,
        maxQuestions: totalQuestions
      });
    }

    console.log(`Generating question ${questionCount} of ${totalQuestions}`);

    // Define isFirstQuestion early for use in real-time analysis
    const isFirstQuestion = !previousQuestions || previousQuestions.length === 0;



    // Question type determination with simple alternation
    let forcedType = null;

    if (isFirstQuestion) {
      forcedType = "behavioral";
      console.log(`🎬 FIRST QUESTION: Starting with behavioral question`);
    } else {
      // Simple alternation between behavioral and technical questions
      const prevTypes = previousQA.map(q => q.type);
      const lastType = prevTypes[prevTypes.length - 1];
      forcedType = lastType === "behavioral" ? "technical" : "behavioral";
      console.log(`🔄 ALTERNATING TYPE: ${forcedType} (previous: ${lastType})`);
    }

    const nextQuestion = await generateSingleQuestion(
      role,
      previousQA,
      skills,
      isFirstQuestion,
      companyType,
      experience,
      forcedType,

    );

    // Phase 1: Handle AI interview termination
    if (nextQuestion.type === "ai_interview_stop") {
      const completedQuestions = questionCount - 1;

      // Additional safeguard: Verify minimum questions requirement
      if (completedQuestions < 5) {
        console.error(`🚨 CRITICAL ERROR: AI attempted to terminate after only ${completedQuestions} questions! This should never happen. Continuing interview instead.`);
        // Force continue by generating a fallback question
        const fallbackResponse = {
          success: true,
          question: "Tell me about a challenging project you've worked on and how you overcame the difficulties.",
          type: "behavioral",
          timerDuration: 90,
          question_number: questionCount,
          total_questions: totalQuestions,
          completed: false,
          _emergency_fallback: true
        };
        return res.status(200).json(fallbackResponse);
      }

      console.log(`🤖 AI TERMINATION: Interview terminated by AI after ${completedQuestions} questions`);
      return res.status(200).json({
        success: true,
        completed: true,
        termination_reason: "ai_sufficient_information",
        questions_completed: completedQuestions,
        message: "Interview completed - sufficient information gathered by AI evaluation"
      });
    }

    // Validate and ensure timerDuration is within acceptable range
    const timerDuration = Math.min(90, Math.max(30, parseInt(nextQuestion.timerDuration) || 90));

    const response = {
      success: true,
      question: nextQuestion.question,
      type: nextQuestion.type,
      timerDuration: timerDuration,
      question_number: questionCount,
      total_questions: totalQuestions,
      completed: false, // BUG FIX: Never mark as completed when successfully returning a question
                       // The interview should only be marked complete when we refuse to generate more questions
      _fallback: nextQuestion._fallback || false
    };

    res.status(200).json(response);

  } catch (error) {
    console.error("Error generating next question:", error);
    
    // Simple fallback
    const fallbackQuestion = {
      // Remove fallback questions; throw error if no questions can be generated
      question: null,
      type: "behavioral",
      timerDuration: 90, // Default fallback duration
      _fallback: true
    };

    const status = error.message === "Interview completed" ? 200 : 500;
    res.status(status).json({
      success: status === 200,
      ...fallbackQuestion,
      completed: error.message === "Interview completed",
      error_details: error.message
    });
  }
};

exports.analyzeAnswer = async (req, res) => {
  try {
    const { question, answer, role, skills, previousQA, videoProfileId } = req.body;

    if (!question || !role) {
      return res.status(400).json({
        message: "Question and role are required fields"
      });
    }

    // Import helpers
    const { analyzeAnswerAndGenerateFollowUp } = require("../utils/helpers");

    console.log("🔍 ANALYZING ANSWER:", {
      question: question.substring(0, 50) + "...",
      answerLength: answer?.length,
      role,
      skillsCount: skills?.length,
      hasVideoProfileId: !!videoProfileId
    });

    // Get analysis and follow-up
    const analysisResult = await analyzeAnswerAndGenerateFollowUp(
      question,
      answer,
      role,
      skills,
      previousQA
    );

    console.log("✅ ANALYSIS COMPLETED:", {
      hasAnalysis: !!analysisResult.analysis,
      hasFollowUp: !!analysisResult.follow_up?.question
    });

    res.json({
      analysis: analysisResult.analysis,
      follow_up: analysisResult.follow_up
    });

  } catch (error) {
    console.error("Error analyzing answer:", error);
    res.status(500).json({
      message: "Failed to analyze answer",
      error: error.message,
      analysis: {
        technical_accuracy: "Analysis failed",
        communication: "Analysis failed",
        knowledge_gaps: ["Unable to analyze response"]
      },
      follow_up: {
        question: "Could you provide more details about your previous answer?",
        reasoning: "Default follow-up due to analysis error",
        expected_focus: "General elaboration"
      }
    });
  }
};
