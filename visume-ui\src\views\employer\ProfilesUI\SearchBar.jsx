// SearchBar.jsx
import React, { useEffect, useState } from "react";
import { Search, Filter } from "lucide-react";
import { HiBriefcase, HiLocationMarker } from "react-icons/hi";
import AdvancedSearchModal from "../components/AdvancedSearchModal";
import { useLocation, useNavigate } from "react-router-dom";

const SearchBar = ({
  setProfiles,
  loading,
  setLoading,
  setSetShowLoadMore,
  getAllProfiles,
  setOldProfiles,
  oldProfiles,
  setViewInteraction,
}) => {
  const [location, setLocation] = useState("");
  const [role, setRole] = useState("");
  const [skills, setSkills] = useState("");
  const [focusedInput, setFocusedInput] = useState(null);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [showAdvancedModal, setShowAdvancedModal] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    experience: "",
    expectedSalary: "",
    currentSalary: "",
    score: "",
  });
  const [countAdvanceFilters, setCountAdvanceFilters] = useState(0);

  const [roleSuggestion, setRoleSuggestion] = useState([]);
  const [skillSuggestion, setSkillSuggestion] = useState([]);
  const [locationSuggestion, setLocationSuggestion] = useState([]);

  const jobRoles = [
    "Software Developer/Engineer",
    "Java Developer",
    "Frontend Developer",
    "Backend Developer",
    "Full Stack Developer",
    "DevOps Engineer",
  ];

  const skillsList = [
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "CSS",
    "HTML",
    "Tailwind CSS",
    "Django",
    "Java",
    "Spring MVC",
  ];

  const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];

  const query = new URLSearchParams(useLocation().search);
  const navigate = useNavigate();

  const handleAddSkill = (skill) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
      setSkills("");
    }
  };

  const handleRemoveSkill = (skill) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  const handleSearch = async () => {
    try {
      if (
        !location.trim() &&
        !role.trim() &&
        !selectedSkills.length &&
        !Object.values(advancedFilters).some((value) => value)
      ) {
        navigate(`filterCandidate`);
        setProfiles([]);
        getAllProfiles(1);
        return;
      }

      setLoading(true);
      const queryParams = new URLSearchParams({
        ...(location && { preferred_location: location }),
        ...(role && { role }),
        ...(selectedSkills.length && {
          selectedSkills: JSON.stringify(selectedSkills),
        }),
        shortlisted: "false",
        ...(advancedFilters.experience && {
          experience: advancedFilters.experience,
        }),
        ...(advancedFilters.expectedSalary && {
          expected_salary: advancedFilters.expectedSalary,
        }),
        ...(advancedFilters.currentSalary && {
          current_salary: advancedFilters.currentSalary,
        }),
        ...(advancedFilters.score && { score: advancedFilters.score }),
      });

      const data = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/filterCandidate?${queryParams.toString()}`
      );

      const res = await data.json();
      setProfiles(res.candidateProfiles);
      setOldProfiles((prev) => {
        const updatedProfiles = [...prev, ...res.candidateProfiles];
        return updatedProfiles.slice(-10);
      });
      navigate(`filterCandidate?${queryParams.toString()}`);
      setViewInteraction((prev) => prev + 1);
      setSetShowLoadMore(false);
    } catch (err) {
      console.error("Error fetching candidate profiles:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleSearch();
    // eslint-disable-next-line
  }, [advancedFilters, selectedSkills]);

  useEffect(() => {
    let count = 0;
    for (const key in advancedFilters) {
      if (advancedFilters[key].trim()) {
        count += 1;
      }
    }
    setCountAdvanceFilters(count);
  }, [advancedFilters]);

  useEffect(() => {
    const preferredLocation = query.get("preferred_location") || "";
    const roleParam = query.get("role") || "";
    const experience = query.get("experience") || "";
    const expectedSalary = query.get("expected_salary") || "";
    const currentSalary = query.get("current_salary") || "";
    const score = query.get("score") || "";
    const skills = query.get("selectedSkills") || "[]";
    const decodedSkills = JSON.parse(decodeURIComponent(skills));

    setLocation(preferredLocation);
    setRole(roleParam);
    setAdvancedFilters({
      experience,
      expectedSalary,
      currentSalary,
      score,
    });
    setSelectedSkills(decodedSkills);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    const advFilter = Object.values(advancedFilters).some((value) => value);
    if (
      !selectedSkills.length &&
      !role &&
      !location &&
      !advFilter &&
      oldProfiles.length
    ) {
      navigate(`filterCandidate`);
      setProfiles((prev) => {
        const updatedProfiles = [...prev, ...oldProfiles].slice(-10);
        const uniqueProfiles = [
          ...new Map(updatedProfiles.map((item) => [item.id, item])).values(),
        ];
        return uniqueProfiles.reverse();
      });
    }
    // eslint-disable-next-line
  }, [selectedSkills, role, location, advancedFilters, oldProfiles]);

  return (
    <div className="relative mx-auto mb-8 w-full max-w-4xl items-center">
      <div className="relative overflow-hidden rounded-xl bg-white p-5 shadow-md">
        {/* Gradient background overlay - Simplified */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/10 via-transparent to-indigo-50/10 rounded-xl"></div>
        
        <div className="relative z-10 flex w-full flex-col gap-3"> {/* Reduced gap */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-start"> {/* Reduced gap */}
            {/* Inputs Container */}
            <div className="flex-1 space-y-2.5 sm:grid sm:grid-cols-3 sm:gap-2.5 sm:space-y-0"> {/* Reduced gap and space-y */}
              {/* Location Input */}
              <div className={`relative flex items-center rounded-lg px-3 py-2.5 border transition-all duration-200 ${
                focusedInput === "location"
                  ? "border-blue-400 bg-blue-50"
                  : "border-gray-300 bg-white hover:border-gray-400"
              }`}>
                <HiLocationMarker
                  size={18}
                  className={focusedInput === "location" ? "text-blue-600" : "text-gray-400"}
                />
                <input
                  type="text"
                  placeholder="Location"
                  value={location}
                  onChange={(e) => {
                    setLocation(e.target.value);
                    if (e.target.value) {
                      const locationSuggest = locations.filter((val) =>
                        val.toLowerCase().includes(e.target.value.toLowerCase())
                      );
                      setLocationSuggestion(locationSuggest);
                    } else {
                      setLocationSuggestion([]);
                    }
                  }}
                  className="bg-transparent w-full py-1 pl-3 text-sm sm:text-base focus:outline-none text-gray-800 placeholder-gray-500 font-sora"
                  onFocus={() => setFocusedInput("location")}
                  onBlur={() => setFocusedInput(null)}
                />
              </div>
              {locationSuggestion && locationSuggestion.length ? (
                <div className="absolute top-16 z-50 flex max-h-[250px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-md">
                  {locationSuggestion.slice(0, 10).map((e, index) => (
                    <span
                      onClick={() => {
                        setLocation(e);
                        setLocationSuggestion([]);
                      }}
                      key={index}
                      className="cursor-pointer px-3 py-2 text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:text-blue-700 first:rounded-t-lg last:rounded-b-lg font-sora"
                    >
                      {e}
                    </span>
                  ))}
                </div>
              ) : undefined}
              
              {/* Role Input */}
              <div className={`relative flex items-center rounded-lg px-3 py-2.5 border transition-all duration-200 ${
                focusedInput === "role"
                  ? "border-blue-400 bg-blue-50"
                  : "border-gray-300 bg-white hover:border-gray-400"
              }`}>
                <HiBriefcase
                  size={18}
                  className={focusedInput === "role" ? "text-blue-600" : "text-gray-400"}
                />
                <input
                  type="text"
                  placeholder="Role"
                  value={role}
                  onChange={(e) => {
                    setRole(e.target.value);
                    if (e.target.value) {
                      const roleSuggest = jobRoles.filter((val) =>
                        val.toLowerCase().includes(e.target.value.toLowerCase())
                      );
                      setRoleSuggestion(roleSuggest);
                    } else {
                      setRoleSuggestion([]);
                    }
                  }}
                  className="bg-transparent w-full py-1 pl-3 text-sm focus:outline-none sm:text-base text-gray-800 placeholder-gray-500 font-sora"
                  onFocus={() => setFocusedInput("role")}
                  onBlur={() => setFocusedInput(null)}
                />
              </div>
              {roleSuggestion && roleSuggestion.length ? (
                <div className="absolute left-[25%] top-16 z-50 flex max-h-[250px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-md">
                  {roleSuggestion.slice(0, 10).map((e, index) => (
                    <span
                      onClick={() => {
                        setRole(e);
                        setRoleSuggestion([]);
                      }}
                      key={index}
                      className="cursor-pointer px-3 py-2 text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:text-blue-700 first:rounded-t-lg last:rounded-b-lg font-sora"
                    >
                      {e}
                    </span>
                  ))}
                </div>
              ) : undefined}
              
              {/* Skills Input */}
              <div className={`relative flex items-center rounded-lg px-3 py-2.5 border transition-all duration-200 ${
                focusedInput === "skills"
                  ? "border-blue-400 bg-blue-50"
                  : "border-gray-300 bg-white hover:border-gray-400"
              }`}>
                <Filter
                  size={18}
                  className={focusedInput === "skills" ? "text-blue-600" : "text-gray-400"}
                />
                <input
                  type="text"
                  placeholder="Skills"
                  value={skills}
                  onChange={(e) => {
                    setSkills(e.target.value);
                    if (e.target.value) {
                      const skillSuggest = skillsList.filter((val) =>
                        val.toLowerCase().includes(e.target.value.toLowerCase())
                      );
                      setSkillSuggestion(skillSuggest);
                    } else {
                      setSkillSuggestion([]);
                    }
                  }}
                  className="bg-transparent w-full py-1 pl-3 text-sm focus:outline-none sm:text-base text-gray-800 placeholder-gray-500 font-sora"
                  onFocus={() => setFocusedInput("skills")}
                  onBlur={() => setFocusedInput(null)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleAddSkill(skills);
                    }
                  }}
                />
              </div>
              {skillSuggestion && skillSuggestion.length ? (
                <div className="absolute left-[50%] top-16 z-50 flex max-h-[250px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-md">
                  {skillSuggestion.slice(0, 10).map((e, index) => (
                    <span
                      onClick={() => {
                        setSkills(e);
                        setSkillSuggestion([]);
                      }}
                      key={index}
                      className="cursor-pointer px-3 py-2 text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:text-blue-700 first:rounded-t-lg last:rounded-b-lg font-sora"
                    >
                      {e}
                    </span>
                  ))}
                </div>
              ) : undefined}
            </div>
            
            {/* Buttons Container */}
            <div className="flex gap-2.5 sm:ml-2.5"> {/* Reduced gap and ml */}
              <button
                onClick={() => setShowAdvancedModal(true)}
                className="relative flex items-center justify-center gap-1.5 rounded-lg border border-gray-300 px-3.5 py-2.5 text-sm font-medium text-gray-700 transition-all duration-200 hover:border-gray-400 hover:bg-gray-50 font-sora"
              >
                <Filter size={16} />
                Filters
                {countAdvanceFilters > 0 && (
                  <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white"> {/* Smaller badge */}
                    {countAdvanceFilters}
                  </span>
                )}
              </button>
              <button
                onClick={loading ? undefined : handleSearch}
                className="flex items-center justify-center gap-1.5 rounded-lg bg-blue-600 px-5 py-2.5 text-sm font-semibold text-white transition-all duration-200 hover:bg-blue-700 shadow-md disabled:opacity-50 font-sora"
                disabled={loading}
              >
                {loading ? (
                  <svg
                    className="h-4 w-4 animate-spin text-white"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                ) : (
                  <>
                    <Search size={16} />
                    Search
                  </>
                )}
              </button>
            </div>
          </div>
          
          {/* Selected Skills */}
          {selectedSkills.length > 0 && (
            <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200"> {/* Changed border color */}
              {selectedSkills.map((skill) => (
                <span
                  key={skill}
                  className="flex items-center rounded-md bg-gray-100 px-2.5 py-1 text-sm text-gray-700 border border-gray-200 font-sora"
                >
                  {skill}
                  <button
                    className="ml-1.5 text-gray-500 hover:text-gray-700 transition-colors duration-200"
                    onClick={() => handleRemoveSkill(skill)}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {/* Advanced Filters Display */}
      <div className="mt-2.5 flex items-center justify-start gap-2"> {/* Reduced margin-top */}
        {Object.entries(advancedFilters)
          .filter(([key, value]) => value)
          .map(([key, value], index) => (
            <div
              key={index}
              className="relative flex items-center space-x-1.5 rounded-md bg-gray-700 px-3 py-1.5 text-white shadow-sm"
            >
              <span className="text-sm font-medium font-sora">
                {`${key}: ${value}`}
              </span>
              <button
                type="button"
                className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white hover:bg-red-600 transition-colors duration-200"
                onClick={() => {
                  const newFilters = { ...advancedFilters };
                  newFilters[key] = "";
                  setAdvancedFilters(newFilters);
                }}
              >
                ×
              </button>
            </div>
          ))}
      </div>
      
      <AdvancedSearchModal
        isOpen={showAdvancedModal}
        onClose={() => setShowAdvancedModal(false)}
        advancedFilters={advancedFilters}
        setAdvancedFilters={setAdvancedFilters}
        onApplyFilters={(filters) => setAdvancedFilters(filters)}
      />
    </div>
  );
};

export default SearchBar;