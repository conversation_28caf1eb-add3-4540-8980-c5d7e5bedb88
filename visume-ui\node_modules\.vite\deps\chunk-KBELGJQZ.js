import {
  require_yang
} from "./chunk-7GAPVT2R.js";
import {
  require_zig
} from "./chunk-GDTS6Z7G.js";
import {
  require_core
} from "./chunk-WA7RXIRA.js";
import {
  require_web_idl
} from "./chunk-PSNKLOCO.js";
import {
  require_wiki
} from "./chunk-C75MSMZ5.js";
import {
  require_wolfram
} from "./chunk-MQ4BYTTS.js";
import {
  require_wren
} from "./chunk-TPE24YSG.js";
import {
  require_xeora
} from "./chunk-XY6F34Q3.js";
import {
  require_xml_doc
} from "./chunk-UE7GEH5E.js";
import {
  require_xojo
} from "./chunk-7NK7SL53.js";
import {
  require_xquery
} from "./chunk-PZGQ6BP7.js";
import {
  require_vala
} from "./chunk-FXAWPONO.js";
import {
  require_velocity
} from "./chunk-23DXKUAN.js";
import {
  require_verilog
} from "./chunk-XBPBZVLQ.js";
import {
  require_vhdl
} from "./chunk-WQIUYWQK.js";
import {
  require_vim
} from "./chunk-DOXOKMEC.js";
import {
  require_visual_basic
} from "./chunk-K2EQHNBN.js";
import {
  require_warpscript
} from "./chunk-C465NPPU.js";
import {
  require_wasm
} from "./chunk-HXH7KUL4.js";
import {
  require_tsx
} from "./chunk-2C3UGSK7.js";
import {
  require_tt2
} from "./chunk-YMBCMPDH.js";
import {
  require_twig
} from "./chunk-FQJ475AN.js";
import {
  require_typoscript
} from "./chunk-VBKXWOLX.js";
import {
  require_unrealscript
} from "./chunk-QD6GYSU7.js";
import {
  require_uorazor
} from "./chunk-YIMLQ2DH.js";
import {
  require_uri
} from "./chunk-PF5FKE6T.js";
import {
  require_v
} from "./chunk-HLBUBCUP.js";
import {
  require_t4_vb
} from "./chunk-LK6DBK4F.js";
import {
  require_vbnet
} from "./chunk-EMXBXY4F.js";
import {
  require_tap
} from "./chunk-7ENT44KC.js";
import {
  require_yaml
} from "./chunk-HQRD3H4X.js";
import {
  require_tcl
} from "./chunk-LKUA4M2T.js";
import {
  require_textile
} from "./chunk-EMXE52T4.js";
import {
  require_toml
} from "./chunk-QX6QSDZF.js";
import {
  require_tremor
} from "./chunk-MZUNO7EN.js";
import {
  require_sqf
} from "./chunk-GNB3WZXM.js";
import {
  require_squirrel
} from "./chunk-F7X3L7VE.js";
import {
  require_stan
} from "./chunk-ZQOACKXN.js";
import {
  require_stylus
} from "./chunk-DPSA3DB3.js";
import {
  require_swift
} from "./chunk-YAC55IWD.js";
import {
  require_systemd
} from "./chunk-GLR4YHKL.js";
import {
  require_t4_cs
} from "./chunk-VF7JX62F.js";
import {
  require_t4_templating
} from "./chunk-6DWPBQKG.js";
import {
  require_smarty
} from "./chunk-LXHUB7MW.js";
import {
  require_sml
} from "./chunk-UHLD5WAK.js";
import {
  require_solidity
} from "./chunk-A3WYC3PK.js";
import {
  require_solution_file
} from "./chunk-T3TBSKK5.js";
import {
  require_soy
} from "./chunk-J3WJADO7.js";
import {
  require_sparql
} from "./chunk-DLUVQ2DM.js";
import {
  require_turtle
} from "./chunk-O53BF5B4.js";
import {
  require_splunk_spl
} from "./chunk-HUZV6PAK.js";
import {
  require_rust
} from "./chunk-47WIR7ZI.js";
import {
  require_sas
} from "./chunk-XGTOFK54.js";
import {
  require_sass
} from "./chunk-GX3BWDHG.js";
import {
  require_scala
} from "./chunk-XJHOSKTU.js";
import {
  require_scss
} from "./chunk-KMV5KWCT.js";
import {
  require_shell_session
} from "./chunk-ZUE2DALX.js";
import {
  require_smali
} from "./chunk-JRZKQCTK.js";
import {
  require_smalltalk
} from "./chunk-KHFYVJXY.js";
import {
  require_reason
} from "./chunk-GQANJMWK.js";
import {
  require_regex
} from "./chunk-7E2YQ44W.js";
import {
  require_rego
} from "./chunk-PCCACH24.js";
import {
  require_renpy
} from "./chunk-62L7PY7K.js";
import {
  require_rest
} from "./chunk-X6VRN3G6.js";
import {
  require_rip
} from "./chunk-EM4WIAAW.js";
import {
  require_roboconf
} from "./chunk-W4UG4FSI.js";
import {
  require_robotframework
} from "./chunk-HK2UUJRM.js";
import {
  require_purescript
} from "./chunk-47KTFR2G.js";
import {
  require_python
} from "./chunk-TQMLO6BB.js";
import {
  require_q
} from "./chunk-JQHULBPD.js";
import {
  require_qml
} from "./chunk-7C76A3JG.js";
import {
  require_qore
} from "./chunk-LKZMKL72.js";
import {
  require_qsharp
} from "./chunk-2VZICLEZ.js";
import {
  require_r
} from "./chunk-YCRRMXPV.js";
import {
  require_racket
} from "./chunk-SOMNBGPK.js";
import {
  require_promql
} from "./chunk-KR3KE7C3.js";
import {
  require_properties
} from "./chunk-IMBEJJ4Q.js";
import {
  require_protobuf
} from "./chunk-Z3N24GN5.js";
import {
  require_psl
} from "./chunk-VHCVCXKT.js";
import {
  require_pug
} from "./chunk-WZ6J2F4D.js";
import {
  require_puppet
} from "./chunk-UVTAAJ6Z.js";
import {
  require_pure
} from "./chunk-YOKJMARF.js";
import {
  require_purebasic
} from "./chunk-W7DOJLYV.js";
import {
  require_perl
} from "./chunk-KRFO7TD3.js";
import {
  require_php_extras
} from "./chunk-AGQNO7GA.js";
import {
  require_phpdoc
} from "./chunk-ROKRV666.js";
import {
  require_plsql
} from "./chunk-IFATEM2C.js";
import {
  require_powerquery
} from "./chunk-KVXA7I4R.js";
import {
  require_powershell
} from "./chunk-HC37RN5S.js";
import {
  require_processing
} from "./chunk-L6XAG6LR.js";
import {
  require_prolog
} from "./chunk-S6WEZ7K3.js";
import {
  require_openqasm
} from "./chunk-JQ42VARQ.js";
import {
  require_oz
} from "./chunk-CHY44DQP.js";
import {
  require_parigp
} from "./chunk-JTMKABK7.js";
import {
  require_parser
} from "./chunk-ZXT2WU4I.js";
import {
  require_pascal
} from "./chunk-M6AWFTJM.js";
import {
  require_pascaligo
} from "./chunk-JHVBAVEG.js";
import {
  require_pcaxis
} from "./chunk-5ZDBTLPY.js";
import {
  require_peoplecode
} from "./chunk-NGDUXYGU.js";
import {
  require_nevod
} from "./chunk-7UT2Y5SU.js";
import {
  require_nginx
} from "./chunk-LULDDJVB.js";
import {
  require_nim
} from "./chunk-YGO5EK2S.js";
import {
  require_nix
} from "./chunk-AJMNFJFK.js";
import {
  require_nsis
} from "./chunk-TW4EPREB.js";
import {
  require_objectivec
} from "./chunk-HZ635PIW.js";
import {
  require_ocaml
} from "./chunk-VBA5ZU44.js";
import {
  require_opencl
} from "./chunk-VHEK47C3.js";
import {
  require_monkey
} from "./chunk-WLWWINO5.js";
import {
  require_moonscript
} from "./chunk-UC3GFYIU.js";
import {
  require_n1ql
} from "./chunk-RKNUCNTA.js";
import {
  require_n4js
} from "./chunk-N3ZNS2HL.js";
import {
  require_nand2tetris_hdl
} from "./chunk-OT46VSPE.js";
import {
  require_naniscript
} from "./chunk-T24H34M6.js";
import {
  require_nasm
} from "./chunk-XEMXZN7G.js";
import {
  require_neon
} from "./chunk-GHPKKRXT.js";
import {
  require_markdown
} from "./chunk-RDY3EEHM.js";
import {
  require_matlab
} from "./chunk-BX3WDRLT.js";
import {
  require_maxscript
} from "./chunk-C7KCAZWS.js";
import {
  require_mel
} from "./chunk-K3RR3R47.js";
import {
  require_mermaid
} from "./chunk-27YDV3GP.js";
import {
  require_mizar
} from "./chunk-U23RBVOD.js";
import {
  require_mongodb
} from "./chunk-LETYJ7CF.js";
import {
  require_liquid
} from "./chunk-KH5DIQOV.js";
import {
  require_lisp
} from "./chunk-NEXCJ6VL.js";
import {
  require_livescript
} from "./chunk-SWS3U6WQ.js";
import {
  require_llvm
} from "./chunk-FEV6ONNW.js";
import {
  require_log
} from "./chunk-DEZPVJMZ.js";
import {
  require_lolcode
} from "./chunk-JR5VJTSO.js";
import {
  require_magma
} from "./chunk-4L5WXDDE.js";
import {
  require_makefile
} from "./chunk-IQIRFHDT.js";
import {
  require_kumir
} from "./chunk-K2LC6C7Q.js";
import {
  require_kusto
} from "./chunk-KNOLGZ4W.js";
import {
  require_latex
} from "./chunk-64FNEBO5.js";
import {
  require_latte
} from "./chunk-6C3HQ2QC.js";
import {
  require_php
} from "./chunk-6DG5FTXU.js";
import {
  require_less
} from "./chunk-JQQ67JUV.js";
import {
  require_lilypond
} from "./chunk-DYGDW2ZH.js";
import {
  require_scheme
} from "./chunk-DQPYOCKS.js";
import {
  require_json5
} from "./chunk-EG7MWGPT.js";
import {
  require_jsonp
} from "./chunk-5EQ7J6R2.js";
import {
  require_jsstacktrace
} from "./chunk-X2QWXD5A.js";
import {
  require_jsx
} from "./chunk-JQNY7KYM.js";
import {
  require_julia
} from "./chunk-CAR6PAX5.js";
import {
  require_keepalived
} from "./chunk-3HTN6RIQ.js";
import {
  require_keyman
} from "./chunk-ZU43FKQE.js";
import {
  require_kotlin
} from "./chunk-E43UHQMA.js";
import {
  require_jexl
} from "./chunk-AM7MO6AK.js";
import {
  require_jolie
} from "./chunk-F7ZNGQYH.js";
import {
  require_jq
} from "./chunk-6XQZGLQL.js";
import {
  require_js_extras
} from "./chunk-QW2V2BX4.js";
import {
  require_js_templates
} from "./chunk-YBRQ4DL5.js";
import {
  require_jsdoc
} from "./chunk-HQ5N5ZTI.js";
import {
  require_typescript
} from "./chunk-RERY2Y4W.js";
import {
  require_json
} from "./chunk-U2YO467X.js";
import {
  require_ini
} from "./chunk-IF7GBPYB.js";
import {
  require_io
} from "./chunk-2E6UPQCF.js";
import {
  require_j
} from "./chunk-3MHQTYLB.js";
import {
  require_javadoc
} from "./chunk-MJ3WHKBX.js";
import {
  require_java
} from "./chunk-4YDK6PE3.js";
import {
  require_javadoclike
} from "./chunk-35CXGZVM.js";
import {
  require_javastacktrace
} from "./chunk-ASHU5EIP.js";
import {
  require_http
} from "./chunk-I446DEI3.js";
import {
  require_ichigojam
} from "./chunk-ZS7VGD6V.js";
import {
  require_icon
} from "./chunk-LCWZMC3J.js";
import {
  require_icu_message_format
} from "./chunk-O5TNJQJ5.js";
import {
  require_idris
} from "./chunk-DKKFEFEP.js";
import {
  require_iecst
} from "./chunk-43PGEU5L.js";
import {
  require_ignore
} from "./chunk-QE6KZNHG.js";
import {
  require_inform7
} from "./chunk-MSLR2UJ3.js";
import {
  require_handlebars
} from "./chunk-MYX7L3SA.js";
import {
  require_haskell
} from "./chunk-MYWP4PYR.js";
import {
  require_haxe
} from "./chunk-NBTH44RR.js";
import {
  require_hcl
} from "./chunk-5KOO5W5O.js";
import {
  require_hlsl
} from "./chunk-XTELNYFS.js";
import {
  require_hoon
} from "./chunk-PI52H5LW.js";
import {
  require_hpkp
} from "./chunk-OI4YQ7BB.js";
import {
  require_hsts
} from "./chunk-TTIE2PZG.js";
import {
  require_glsl
} from "./chunk-5SHHDG4K.js";
import {
  require_gml
} from "./chunk-WFKK6XGO.js";
import {
  require_gn
} from "./chunk-RJAXL3OG.js";
import {
  require_go_module
} from "./chunk-FZL76HZY.js";
import {
  require_go
} from "./chunk-FLCIXLSQ.js";
import {
  require_graphql
} from "./chunk-5I4RGLBT.js";
import {
  require_groovy
} from "./chunk-PN4TWTUS.js";
import {
  require_haml
} from "./chunk-M3YVTZYS.js";
import {
  require_fsharp
} from "./chunk-6XB34QOC.js";
import {
  require_ftl
} from "./chunk-X4T6ICGK.js";
import {
  require_gap
} from "./chunk-VFI4FQHJ.js";
import {
  require_gcode
} from "./chunk-5M6WRCAG.js";
import {
  require_gdscript
} from "./chunk-V6X6QFUZ.js";
import {
  require_gedcom
} from "./chunk-RXCN7OLR.js";
import {
  require_gherkin
} from "./chunk-DXQN2SPT.js";
import {
  require_git
} from "./chunk-L33LKZ3G.js";
import {
  require_etlua
} from "./chunk-6GGEGALG.js";
import {
  require_lua
} from "./chunk-QOGIVFFN.js";
import {
  require_excel_formula
} from "./chunk-66WLCSJB.js";
import {
  require_factor
} from "./chunk-S7QYCS4C.js";
import {
  require_false
} from "./chunk-25WQJZE7.js";
import {
  require_firestore_security_rules
} from "./chunk-MEVSST6V.js";
import {
  require_flow
} from "./chunk-YKBUJ73Y.js";
import {
  require_fortran
} from "./chunk-ZUNK7PXL.js";
import {
  require_ebnf
} from "./chunk-Z2HIYMML.js";
import {
  require_editorconfig
} from "./chunk-LP6C2BYW.js";
import {
  require_eiffel
} from "./chunk-3ATADWQF.js";
import {
  require_ejs
} from "./chunk-W5A475DZ.js";
import {
  require_elixir
} from "./chunk-D2F6TLTY.js";
import {
  require_elm
} from "./chunk-H2GFH55K.js";
import {
  require_erb
} from "./chunk-6WBFN2NF.js";
import {
  require_erlang
} from "./chunk-UWD2TVHT.js";
import {
  require_dax
} from "./chunk-EOW5KDRC.js";
import {
  require_dhall
} from "./chunk-6SHYISZK.js";
import {
  require_diff
} from "./chunk-7UMOUH2T.js";
import {
  require_django
} from "./chunk-P4B4QTCC.js";
import {
  require_markup_templating
} from "./chunk-7NDLZG53.js";
import {
  require_dns_zone_file
} from "./chunk-Q2RZSPCZ.js";
import {
  require_docker
} from "./chunk-JD6J6W3Q.js";
import {
  require_dot
} from "./chunk-ADVU2AJF.js";
import {
  require_csp
} from "./chunk-HNXCM5VG.js";
import {
  require_css_extras
} from "./chunk-WYUJQNUA.js";
import {
  require_csv
} from "./chunk-6KMTGIUD.js";
import {
  require_cypher
} from "./chunk-N72X2WP6.js";
import {
  require_d
} from "./chunk-XPYJNNPM.js";
import {
  require_dart
} from "./chunk-GDDNRK5B.js";
import {
  require_dataweave
} from "./chunk-ABIC2DTP.js";
import {
  require_cmake
} from "./chunk-5GSPWZCP.js";
import {
  require_cobol
} from "./chunk-44767WFJ.js";
import {
  require_coffeescript
} from "./chunk-6A7HHEJI.js";
import {
  require_concurnas
} from "./chunk-HAFOYCKU.js";
import {
  require_coq
} from "./chunk-XU76NDPW.js";
import {
  require_crystal
} from "./chunk-EIF43MMU.js";
import {
  require_ruby
} from "./chunk-SSSVXVQE.js";
import {
  require_cshtml
} from "./chunk-IYFFZTR3.js";
import {
  require_brightscript
} from "./chunk-T4IKFUM7.js";
import {
  require_bro
} from "./chunk-CUUJKXCS.js";
import {
  require_bsl
} from "./chunk-ONE5EQUN.js";
import {
  require_cfscript
} from "./chunk-5O3PUNJM.js";
import {
  require_chaiscript
} from "./chunk-AHKFYHTU.js";
import {
  require_cil
} from "./chunk-IHKG3BNU.js";
import {
  require_clojure
} from "./chunk-YCLN47N6.js";
import {
  require_basic
} from "./chunk-ZKY73EYK.js";
import {
  require_batch
} from "./chunk-ZTRMFGLD.js";
import {
  require_bbcode
} from "./chunk-7XECU3EG.js";
import {
  require_bicep
} from "./chunk-NYPJD2TE.js";
import {
  require_birb
} from "./chunk-CVUIUBYZ.js";
import {
  require_bison
} from "./chunk-X4BPSRAX.js";
import {
  require_bnf
} from "./chunk-UOOB375K.js";
import {
  require_brainfuck
} from "./chunk-23KTHKAA.js";
import {
  require_asmatmel
} from "./chunk-LIEFT4MO.js";
import {
  require_aspnet
} from "./chunk-ISIOQKKH.js";
import {
  require_csharp
} from "./chunk-6XU5YIVJ.js";
import {
  require_autohotkey
} from "./chunk-34YHHX2W.js";
import {
  require_autoit
} from "./chunk-4TSPCFIR.js";
import {
  require_avisynth
} from "./chunk-DX44HEBN.js";
import {
  require_avro_idl
} from "./chunk-XCTLSOXW.js";
import {
  require_bash
} from "./chunk-GESAY53S.js";
import {
  require_applescript
} from "./chunk-RVOHTDSP.js";
import {
  require_aql
} from "./chunk-TC2PGLLC.js";
import {
  require_arduino
} from "./chunk-T6SQQTNO.js";
import {
  require_cpp
} from "./chunk-XPZJNBFI.js";
import {
  require_c
} from "./chunk-NMCUD3BH.js";
import {
  require_arff
} from "./chunk-MGHFYFOA.js";
import {
  require_asciidoc
} from "./chunk-4D3AROPX.js";
import {
  require_asm6502
} from "./chunk-Y4NNVFOH.js";
import {
  require_ada
} from "./chunk-G2KRM2J2.js";
import {
  require_agda
} from "./chunk-WVGECC56.js";
import {
  require_al
} from "./chunk-SFNZKB3L.js";
import {
  require_antlr4
} from "./chunk-S6FQSOKM.js";
import {
  require_apacheconf
} from "./chunk-CLT22NEA.js";
import {
  require_apex
} from "./chunk-ERCZVFCV.js";
import {
  require_sql
} from "./chunk-HNKX3MW6.js";
import {
  require_apl
} from "./chunk-TG4XMO3I.js";
import {
  require_abap
} from "./chunk-4SLBFPMN.js";
import {
  require_abnf
} from "./chunk-IIVNSTSJ.js";
import {
  require_actionscript
} from "./chunk-RNUS6LNQ.js";
import {
  __commonJS
} from "./chunk-2GTGKKMZ.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-KBELGJQZ.js.map
